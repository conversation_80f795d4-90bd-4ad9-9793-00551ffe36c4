seed: 0
detect_anomaly: false
test_iterations: []
checkpoint_iterations: []

smpl_pkl_path: ./smpl_model/smplx/SMPLX_NEUTRAL.npz

background: [1., 1., 1.]
random_background: true

# dataset
#train_cam_ids: [001,002,003,004,005,006,007,008,009,010,011,012,013,014,015,016,017,018,019,020,021,022,023,024,025,026,027,028,029,030,031,032,033,034,035,036,037,038,039,040,041,042,043,044,045,046,047,048,049,050,051,052,053,054,055,056,057,058,059]
train_cam_ids: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58]  
num_train_frame: 932
begin_ith_frame: 0
frame_interval: 1
image_scaling: 1
data_in_memory: false

test:
  cam_ids: [18]
  num_frame: 500
  begin_ith_frame: 0
  frame_interval: 1
  image_scaling: 1

# optimization
iterations: 20_000
position_lr: 0.00016
opacity_lr: 0.0005
scaling_lr: 0.0005
rotation_lr: 0.0005
color_lr: 0.0005
xyz_offset_lr: 0.001
encoder_lr: 0.0005

iteration_sh_degree: 250000

# loss
lambda_lpips: 0.1
iteration_lpips: 6000
iteration_lpips_random_patch: 300_000
lambda_scaling: 1
scaling_threshold: 0.01
lambda_dxyz_smooth: 0.1

# head-body separation loss weights  
lambda_head_body_separation: 0.01
lambda_head_body_feature_diff: 1.0
lambda_head_body_spatial_sep: 1.0
lambda_head_consistency: 0.05
iteration_head_body_loss: 1000

init_num_gs: 200_000

# anchor points and control points
num_verts: 10000
num_features: 300

# iteration to start optimize the basis
iteration_dxyz_basis: 2000
iteration_gsparam_basis: 2000

