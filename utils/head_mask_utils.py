#!/usr/bin/env python3
"""
正确的头部掩膜生成工具
结合TalkBody4D的投影算法和GaussianSMPLXAvatars的头部提取方法
"""

import numpy as np
import torch
import json
import os
import cv2

def load_flame_vertex_ids():
    """从SMPL-X__FLAME_vertex_ids.npy文件加载FLAME头部顶点索引"""
    flame_ids_path = '/home/<USER>/code/mmlphuman/smpl_model/SMPL-X__FLAME_vertex_ids.npy'
    
    try:
        flame_vertex_ids = np.load(flame_ids_path)
        print(f"✓ 成功加载FLAME顶点索引: {len(flame_vertex_ids)} 个头部顶点")
        return flame_vertex_ids.astype(np.int64)
    except Exception as e:
        print(f"❌ 无法加载FLAME顶点索引: {e}")
        return None

def extract_head_faces_from_smplx(smplx_faces, flame_vertex_ids):
    """
    从SMPL-X面中提取头部面
    根据GaussianSMPLXAvatars的方法: 只有当面的所有顶点都属于头部时，才算头部面
    
    Args:
        smplx_faces: SMPL-X的所有面 [F, 3]
        flame_vertex_ids: FLAME头部顶点索引 [N]
        
    Returns:
        head_faces: 头部面 [head_F, 3]
        head_face_indices: 头部面在原faces中的索引
    """
    if isinstance(smplx_faces, torch.Tensor):
        smplx_faces = smplx_faces.cpu().numpy()
    
    # 创建头部顶点索引的set用于快速查找
    flame_vertex_set = set(flame_vertex_ids)
    
    # 找到所有顶点都在头部的面
    head_face_mask = np.all(np.isin(smplx_faces, flame_vertex_ids), axis=1)
    head_faces = smplx_faces[head_face_mask]
    head_face_indices = np.where(head_face_mask)[0]
    
    print(f"✓ 从 {len(smplx_faces)} 个总面中提取出 {len(head_faces)} 个头部面")
    return head_faces, head_face_indices

def project_vertices_talkbody4d_style(vertices, K, R, T):
    """
    使用TalkBody4D的正确投影方法
    
    Args:
        vertices: 3D顶点 [N, 3]
        K: 相机内参 [3, 3]  
        R: 相机旋转矩阵 [3, 3]
        T: 相机平移向量 [3, 1] 或 [3]
        
    Returns:
        pixels: 投影后的2D像素坐标 [N, 2]
        valid_mask: 有效顶点掩膜 [N]
    """
    if isinstance(vertices, torch.Tensor):
        vertices = vertices.detach().cpu().numpy()
    if isinstance(K, torch.Tensor):
        K = K.detach().cpu().numpy()
    if isinstance(R, torch.Tensor):
        R = R.detach().cpu().numpy()
    if isinstance(T, torch.Tensor):
        T = T.detach().cpu().numpy()
    
    # 确保T是列向量
    if T.ndim == 1:
        T = T.reshape(3, 1)
    
    # TalkBody4D的投影步骤:
    # 1. 世界坐标到相机坐标: R * verts.T + T
    verts_cam = np.matmul(R, vertices.T) + T  # [3, N]
    
    # 2. 过滤在相机前方的顶点 (z > 0)
    valid_mask = verts_cam[2, :] > 0
    
    if not valid_mask.any():
        print("⚠️ 没有顶点在相机前方")
        return np.zeros((len(vertices), 2)), valid_mask
    
    # 3. 相机坐标投影到图像: K * verts_cam
    verts_proj = np.matmul(K, verts_cam)  # [3, N]
    
    # 4. 透视除法: 除以z坐标  
    verts_proj = verts_proj / verts_proj[2:3, :]  # 广播除法 [3, N]
    
    # 5. 取xy坐标并转置为 [N, 2]
    pixels = verts_proj[:2, :].T  # [N, 2]
    
    return pixels, valid_mask

def get_neck_pose_from_full_pose(pose):
    """从完整pose中提取neck pose"""
    if isinstance(pose, np.ndarray):
        pose = torch.from_numpy(pose).float()
    elif isinstance(pose, torch.Tensor):
        pose = pose.float()
    
    # SMPL-X中neck是第12个关节 (index 12)
    neck_pose = pose[12*3:12*3+3]
    return neck_pose

def render_head_mesh_to_mask(vertices, faces, K, w2c, image_height, image_width):
    """
    使用OpenCV渲染头部mesh到mask
    避免PyTorch3D的einsum问题
    
    Args:
        vertices: 头部顶点 [N, 3]
        faces: 头部面 [F, 3] 
        K: 相机内参 [3, 3]
        w2c: 世界到相机变换 [4, 4]
        image_height, image_width: 图像尺寸
        
    Returns:
        head_mask: 头部掩膜 [H, W]
    """
    
    # 转换为numpy
    if isinstance(vertices, torch.Tensor):
        vertices = vertices.detach().cpu().numpy()
    if isinstance(faces, torch.Tensor):
        faces = faces.detach().cpu().numpy()
    if isinstance(K, torch.Tensor):
        K = K.detach().cpu().numpy()
    if isinstance(w2c, torch.Tensor):
        w2c = w2c.detach().cpu().numpy()
    
    # 变换顶点到相机坐标系
    vertices_homo = np.concatenate([vertices, np.ones((vertices.shape[0], 1))], axis=1)
    vertices_cam = (w2c @ vertices_homo.T).T[:, :3]  # [N, 3]
    
    # 过滤在相机前方的顶点
    valid_mask = vertices_cam[:, 2] > 0
    if not valid_mask.any():
        print("Warning: No vertices in front of camera")
        return np.zeros((image_height, image_width), dtype=bool)
    
    # 投影到图像平面
    vertices_img = vertices_cam / vertices_cam[:, 2:3]  # 透视除法
    pixels = (K @ vertices_img.T).T[:, :2]  # [N, 2]
    
    # 创建mask
    head_mask = np.zeros((image_height, image_width), dtype=np.uint8)
    
    # 过滤有效的面（所有顶点都在相机前方且在图像范围内）
    valid_faces = []
    for face in faces:
        face_valid = True
        face_pixels = []
        
        for vertex_idx in face:
            if not valid_mask[vertex_idx]:
                face_valid = False
                break
            
            pixel = pixels[vertex_idx]
            if (pixel[0] < 0 or pixel[0] >= image_width or 
                pixel[1] < 0 or pixel[1] >= image_height):
                face_valid = False
                break
                
            face_pixels.append(pixel)
        
        if face_valid:
            valid_faces.append(np.array(face_pixels, dtype=np.int32))
    
    if not valid_faces:
        print("Warning: No valid faces to render")
        return np.zeros((image_height, image_width), dtype=bool)
    
    # 使用OpenCV填充三角形
    for face_pixels in valid_faces:
        cv2.fillPoly(head_mask, [face_pixels], 255)
    
    print(f"Rendered {len(valid_faces)} head faces to mask")
    return head_mask > 0

def generate_head_mask_from_smpl_vertices(pose, beta, expression, jaw_pose, Rh, Th, K, w2c, image_height, image_width, device='cpu'):
    """
    使用简化的SMPL-X模型生成头部掩膜，参考TalkBody4D实现但避免einsum错误
    """
    try:
        # 强制使用CPU避免多进程CUDA问题
        device = torch.device('cpu')
        
        # 验证并修正输入参数维度 - 关键：使用10维expression避免einsum错误
        if isinstance(expression, np.ndarray):
            expression = torch.from_numpy(expression).float()
        if isinstance(expression, torch.Tensor):
            if expression.numel() == 50:
                # 从50维缩减到10维
                expression = expression[:10]
                print("Reduced expression from 50 to 10 dimensions to avoid einsum error")
            elif expression.numel() == 10:
                print("Using 10-dimensional expression")
            else:
                # 填充或截断到10维
                if expression.numel() < 10:
                    expression = torch.cat([expression, torch.zeros(10 - expression.numel())])
                else:
                    expression = expression[:10]
                print(f"Adjusted expression to 10 dimensions")
            
        # 尝试创建简化的SMPL-X模型
        try:
            import smplx
            
            # 创建简化SMPL-X模型，使用10维expression避免einsum错误
            smplx_model = smplx.create(
                model_path='./smpl_model',
                model_type='smplx',
                gender='neutral',
                num_betas=10,
                num_expression_coeffs=10,  # 关键：使用10维而不是50维
                use_pca=False,
                flat_hand_mean=True,
                batch_size=1
            ).to(device)
            
            print("✓ Created simplified SMPL-X model with 10-dim expression")
            
        except Exception as e:
            print(f"Cannot create SMPL-X model: {e}, head not available")
            return None
        
        # 准备输入参数
        def prepare_param(param, target_device):
            if isinstance(param, np.ndarray):
                param = torch.from_numpy(param).float()
            param = param.to(target_device)
            return param.unsqueeze(0) if param.dim() == 1 else param
        
        pose = prepare_param(pose, device)
        beta = prepare_param(beta, device)
        expression = prepare_param(expression, device)
        jaw_pose = prepare_param(jaw_pose, device)
        Rh = prepare_param(Rh, device)
        Th = prepare_param(Th, device)
        
        print(f"SMPL-X input shapes: pose={pose.shape}, beta={beta.shape}, expression={expression.shape}, jaw_pose={jaw_pose.shape}")
        
        # SMPL-X forward pass
        with torch.no_grad():
            try:
                smpl_output = smplx_model(
                    body_pose=pose[:, 3:66],      # body pose: 63维
                    global_orient=Rh,            # global rotation: 3维
                    betas=beta,                  # shape params: 10维
                    jaw_pose=jaw_pose,           # jaw pose: 3维  
                    expression=expression,       # expression: 10维 (关键改动)
                    return_verts=True
                )
                vertices = smpl_output.vertices[0].cpu().numpy()  # [V, 3]
                print(f"SMPL-X generated {vertices.shape[0]} vertices using simplified model")
                
                # 应用全局变换 (参考TalkBody4D)
                from scipy.spatial.transform import Rotation
                if torch.any(Rh != 0):
                    R_global = Rotation.from_rotvec(Rh[0].cpu().numpy()).as_matrix()
                    vertices = (R_global @ vertices.T).T
                
                vertices = vertices + Th[0].cpu().numpy()
                
            except Exception as e:
                print(f"SMPL-X forward pass failed: {e}, head not available")
                return None
        
        # 获取SMPL-X的完整面信息
        faces = smplx_model.faces.astype(np.int32)
        print(f"SMPL-X has {faces.shape[0]} total faces")
        
        # 获取头部面索引
        head_faces_indices = load_head_faces()
        if not head_faces_indices:
            print("No head faces available")
            return None
        
        # 提取头部面
        head_faces = faces[head_faces_indices]  # [head_F, 3]
        print(f"Using {head_faces.shape[0]} head faces for rendering")
        
        # 渲染头部faces到掩膜
        head_mask = render_head_faces_to_mask(
            vertices, head_faces, K, w2c, image_height, image_width
        )
        
        if head_mask is None or head_mask.sum() == 0:
            print("Head not visible in current camera view, skipping head optimization")
            return None
        
        print(f"Generated simplified SMPL-X head mask: {head_mask.sum()} pixels ({head_mask.sum()/(image_height*image_width)*100:.1f}%)")
        return head_mask
        
    except Exception as e:
        print(f"Warning: Failed to generate SMPL-X head mask: {e}")
        return None

def render_head_faces_to_mask(vertices, faces, K, w2c, image_height, image_width):
    """
    直接渲染头部mesh faces到掩膜，使用TalkBody4D的正确投影算法
    
    Args:
        vertices: 头部顶点 [V, 3]
        faces: 头部面 [F, 3] 
        K: 相机内参 [3, 3]
        w2c: 世界到相机变换 [4, 4]
        image_height, image_width: 图像尺寸
        
    Returns:
        head_mask: 头部掩膜 [H, W]
    """
    
    # 转换为numpy
    if isinstance(vertices, torch.Tensor):
        vertices = vertices.detach().cpu().numpy()
    if isinstance(faces, torch.Tensor):
        faces = faces.detach().cpu().numpy()
    if isinstance(K, torch.Tensor):
        K = K.detach().cpu().numpy()
    if isinstance(w2c, torch.Tensor):
        w2c = w2c.detach().cpu().numpy()
    
    # 从w2c矩阵提取R和T
    # w2c = [R T; 0 1], 我们需要 R 和 T 用于 R*verts + T
    R = w2c[:3, :3]  # [3, 3]
    T = w2c[:3, 3:4]  # [3, 1]
    
    # 使用TalkBody4D的正确投影方法
    # 世界坐标到相机坐标: R * verts + T
    verts_cam = np.matmul(R, vertices.T) + T  # [3, V]
    
    # 过滤在相机前方的顶点
    valid_mask = verts_cam[2, :] > 0
    if not valid_mask.any():
        print("Warning: No vertices in front of camera")
        return np.zeros((image_height, image_width), dtype=bool)
    
    # 相机坐标投影到图像: K * verts_cam  
    verts_proj = np.matmul(K, verts_cam)  # [3, V]
    
    # 透视除法: 除以z坐标
    verts_proj = verts_proj / verts_proj[2:3, :]  # 广播除法
    pixels = verts_proj[:2, :].T  # [V, 2] - 取xy坐标并转置
    
    # 创建mask
    head_mask = np.zeros((image_height, image_width), dtype=np.uint8)
    
    # 渲染每个面到mask
    rendered_faces = 0
    for face in faces:
        # 检查面的所有顶点是否有效
        if all(valid_mask[face]):
            # 获取面的像素坐标
            face_pixels = pixels[face]
            
            # 检查是否都在图像范围内
            if (np.all(face_pixels[:, 0] >= 0) and np.all(face_pixels[:, 0] < image_width) and
                np.all(face_pixels[:, 1] >= 0) and np.all(face_pixels[:, 1] < image_height)):
                
                # 转换为整数像素坐标
                face_pixels_int = np.round(face_pixels).astype(np.int32)
                
                # 使用OpenCV填充三角形
                try:
                    import cv2
                    cv2.fillPoly(head_mask, [face_pixels_int], 255)
                    rendered_faces += 1
                except ImportError:
                    # 如果cv2不可用，使用简单的边界框填充
                    x_min, x_max = face_pixels_int[:, 0].min(), face_pixels_int[:, 0].max()
                    y_min, y_max = face_pixels_int[:, 1].min(), face_pixels_int[:, 1].max()
                    head_mask[y_min:y_max+1, x_min:x_max+1] = 255
                    rendered_faces += 1
    
    if rendered_faces > 0:
        print(f"Successfully rendered {rendered_faces} head faces to mask using TalkBody4D projection")
        return head_mask > 0
    else:
        print("Head not visible in current camera view")
        return None


def generate_head_mask(pose, beta, expression, jaw_pose, Rh, Th, K, w2c, image_height, image_width, body_mask=None, device='cpu'):
    """
    完全模仿TalkBody4D的头部掩膜生成方法 - 最终正确版本
    """
    
    try:
        # 强制使用CPU避免多进程问题
        device = torch.device('cpu')
        
        # 1. 加载FLAME头部顶点索引
        flame_vertex_ids = load_flame_vertex_ids()
        if flame_vertex_ids is None:
            print("❌ 无法获取头部顶点索引")
            return None
        
        # 2. 创建SMPL-X模型
        try:
            import smplx
            
            # 处理expression维度
            if isinstance(expression, np.ndarray):
                expression = torch.from_numpy(expression).float()
            if isinstance(expression, torch.Tensor):
                if expression.numel() == 50:
                    expression = expression[:10]
                    print("✓ 缩减expression从50维到10维")
                elif expression.numel() == 10:
                    print("✓ 使用10维expression")
                else:
                    if expression.numel() < 10:
                        expression = torch.cat([expression, torch.zeros(10 - expression.numel())])
                    else:
                        expression = expression[:10]
                    print("✓ 调整expression到10维")
            
            # 创建SMPL-X模型
            smplx_model = smplx.create(
                model_path='./smpl_model',
                model_type='smplx', 
                gender='neutral',
                num_betas=10,
                num_expression_coeffs=10,
                use_pca=False,
                flat_hand_mean=True,
                batch_size=1
            ).to(device)
            
        except Exception as e:
            print(f"❌ 创建SMPL-X模型失败: {e}")
            return None
        
        # 3. 准备SMPL-X参数
        def prepare_param(param, target_device):
            if isinstance(param, np.ndarray):
                param = torch.from_numpy(param).float()
            param = param.to(target_device)
            return param.unsqueeze(0) if param.dim() == 1 else param
        
        pose = prepare_param(pose, device)
        beta = prepare_param(beta, device)
        expression = prepare_param(expression, device)
        jaw_pose = prepare_param(jaw_pose, device)
        Rh = prepare_param(Rh, device)
        Th = prepare_param(Th, device)
        
        # 4. SMPL-X前向传播
        with torch.no_grad():
            try:
                smpl_output = smplx_model(
                    body_pose=pose[:, 3:66],
                    global_orient=Rh,
                    betas=beta,
                    jaw_pose=jaw_pose,
                    expression=expression,
                    return_verts=True
                )
                vertices = smpl_output.vertices[0].cpu().numpy()  # [N, 3]
                
                # 应用全局变换 - 按照TalkBody4D的方式
                from scipy.spatial.transform import Rotation
                if torch.any(Rh != 0):
                    R_global = Rotation.from_rotvec(Rh[0].cpu().numpy()).as_matrix()
                    vertices = (R_global @ vertices.T).T
                
                vertices = vertices + Th[0].cpu().numpy()
                
            except Exception as e:
                print(f"❌ SMPL-X前向传播失败: {e}")
                return None
        
        # 5. 提取头部面
        smplx_faces = smplx_model.faces.astype(np.int32)
        head_faces, head_face_indices = extract_head_faces_from_smplx(smplx_faces, flame_vertex_ids)
        
        if len(head_faces) == 0:
            print("❌ 没有找到头部面")
            return None
        
        # 6. 从w2c矩阵提取R和T，完全按照TalkBody4D的格式
        if isinstance(w2c, torch.Tensor):
            w2c = w2c.detach().cpu().numpy()
        
        # TalkBody4D使用的是world-to-camera的R和T
        R = w2c[:3, :3]  # [3, 3] 旋转矩阵
        T = w2c[:3, 3]   # [3] 平移向量
        
        # 7. 使用TalkBody4D的精确投影方法
        pixels, valid_mask = talkbody4d_projection_exact(vertices, K, R, T, image_width, image_height)
        
        if pixels is None:
            print("❌ TalkBody4D投影失败")
            return None
        
        # 8. 创建空的掩膜并渲染头部面
        head_mask = np.zeros((image_height, image_width), dtype=np.uint8)
        
        rendered_faces = 0
        for face in head_faces:
            # 检查面的所有顶点是否有效
            if all(valid_mask[face]):
                # 获取面的像素坐标
                face_pixels = pixels[face]  # [3, 2]
                
                try:
                    # 使用OpenCV填充三角形面
                    cv2.fillPoly(head_mask, [face_pixels], 255)
                    rendered_faces += 1
                except Exception as e:
                    continue
        
        if rendered_faces == 0:
            print("❌ 没有成功渲染的面")
            return None
        
        # 转换为bool掩膜
        head_mask_bool = head_mask > 0
        
        # 9. 如果提供了全身掩膜，求交集
        if body_mask is not None:
            if isinstance(body_mask, torch.Tensor):
                body_mask = body_mask.cpu().numpy()
            
            # 头部掩膜 = 头部投影 ∩ 全身掩膜
            head_mask_intersected = head_mask_bool & body_mask
            
            if head_mask_intersected.sum() == 0:
                print("❌ 头部投影与全身掩膜无交集")
                return None
            
            return head_mask_intersected
        
        else:
            return head_mask_bool
            
    except Exception as e:
        print(f"❌ TalkBody4D风格头部掩膜生成失败: {e}")
        return None

def talkbody4d_projection_exact(vertices, K, R, T, img_w, img_h):
    """
    完全模仿TalkBody4D的投影方法，包括所有细节
    对应TalkBody4D_utils.py的第148-157行
    """
    # 确保输入是numpy数组
    if isinstance(vertices, torch.Tensor):
        vertices = vertices.detach().cpu().numpy()
    if isinstance(K, torch.Tensor):
        K = K.detach().cpu().numpy()
    if isinstance(R, torch.Tensor):
        R = R.detach().cpu().numpy()
    if isinstance(T, torch.Tensor):
        T = T.detach().cpu().numpy()
    
    # 确保T是列向量 [3, 1]，完全按照TalkBody4D
    if T.ndim == 1:
        T = T.reshape(3, 1)
    
    # TalkBody4D第148行: smplx_verts_cam = np.matmul(R, verts.T) + T
    smplx_verts_cam = np.matmul(R, vertices.T) + T  # [3, N]
    
    # 检查在相机前方的顶点
    valid_mask = smplx_verts_cam[2, :] > 0
    
    if not valid_mask.any():
        return None, np.zeros(len(vertices), dtype=bool)
    
    # TalkBody4D第150行: smplx_verts_proj = np.matmul(K, smplx_verts_cam)
    smplx_verts_proj = np.matmul(K, smplx_verts_cam)  # [3, N]
    
    # TalkBody4D第152行: smplx_verts_proj /= smplx_verts_proj[2, :]
    smplx_verts_proj /= smplx_verts_proj[2, :]
    
    # TalkBody4D第153行: smplx_verts_proj = smplx_verts_proj[:2, :].T
    smplx_verts_proj = smplx_verts_proj[:2, :].T  # [N, 2]
    
    # TalkBody4D第155行: smplx_verts_proj = np.round(smplx_verts_proj).astype(np.int32)
    smplx_verts_proj_rounded = np.round(smplx_verts_proj).astype(np.int32)
    
    # TalkBody4D第156-157行: clip到图像边界
    smplx_verts_proj_rounded[:, 0] = np.clip(smplx_verts_proj_rounded[:, 0], 0, img_w - 1)
    smplx_verts_proj_rounded[:, 1] = np.clip(smplx_verts_proj_rounded[:, 1], 0, img_h - 1)
    
    # 检查有效像素范围
    in_bounds_mask = (
        (smplx_verts_proj[:, 0] >= 0) & (smplx_verts_proj[:, 0] < img_w) &
        (smplx_verts_proj[:, 1] >= 0) & (smplx_verts_proj[:, 1] < img_h)
    )
    
    # 最终有效顶点：在相机前方且在图像范围内
    final_valid_mask = valid_mask & in_bounds_mask
    
    return smplx_verts_proj_rounded, final_valid_mask

def generate_geometric_head_mask(image_height, image_width):
    """几何估计的头部掩膜作为fallback"""
    head_mask = np.zeros((image_height, image_width), dtype=bool)
    
    # 头部区域参数（基于人体比例）
    head_y_start = int(image_height * 0.05)   # 从5%开始
    head_y_end = int(image_height * 0.35)     # 到35%结束
    head_x_center = image_width // 2
    head_width = int(image_width * 0.25)      # 宽度25%
    
    center_y = (head_y_start + head_y_end) / 2
    center_x = head_x_center
    radius_y = (head_y_end - head_y_start) / 2
    radius_x = head_width / 2
    
    for y in range(head_y_start, head_y_end):
        for x in range(max(0, head_x_center - head_width // 2), 
                      min(image_width, head_x_center + head_width // 2)):
            dx = (x - center_x) / radius_x
            dy = (y - center_y) / radius_y
            if dx*dx + dy*dy <= 1.0:
                head_mask[y, x] = True
    
    print(f"Generated geometric fallback head mask: {head_mask.sum()} pixels ({head_mask.sum()/(image_height*image_width)*100:.1f}%)")
    return head_mask


if __name__ == "__main__":
    # 测试代码
    print("Testing SMPL-X mesh head mask generation...")
    
    # 测试参数
    pose = np.zeros(165, dtype=np.float32)
    beta = np.zeros(10, dtype=np.float32)
    expression = np.zeros(50, dtype=np.float32)
    jaw_pose = np.zeros(3, dtype=np.float32)
    Rh = np.zeros(3, dtype=np.float32)
    Th = np.zeros(3, dtype=np.float32)
    
    K = np.array([[1000, 0, 320], [0, 1000, 240], [0, 0, 1]], dtype=np.float32)
    w2c = np.eye(4, dtype=np.float32)
    image_height, image_width = 480, 640
    
    # 测试头部mask生成
    head_mask = generate_head_mask(
        pose, beta, expression, jaw_pose, Rh, Th,
        K, w2c, image_height, image_width, device='cpu'
    )
    
    print(f"Generated head mask shape: {head_mask.shape}")
    print(f"Head mask pixels: {head_mask.sum()}")
    print("✅ SMPL-X mesh head mask generation test completed!")
    456123