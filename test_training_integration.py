#!/usr/bin/env python3
"""
测试头部身体分离功能在训练中的集成
"""

import torch
import numpy as np
import os
import sys

# 添加项目路径
sys.path.append('/home/<USER>/code/mmlphuman')

def test_training_integration():
    """测试训练集成"""
    print("开始测试训练集成...")
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.loss_utils import head_body_separation_loss, head_consistency_loss
        from utils.config_utils import Config
        
        print("✓ 成功导入训练相关模块")
        
        # 创建模拟的GaussianModel
        gaussians = GaussianModel()
        
        # 模拟初始化头部身体分离
        gaussians.expression = torch.zeros(10, dtype=torch.float32)
        gaussians.jaw_pose = torch.zeros(3, dtype=torch.float32)
        gaussians.smpl_poses_cuda = torch.zeros(165, dtype=torch.float32, device='cuda')
        
        # 模拟分离的编码器 - 使用3D参数 (batch, output, input)
        # 模拟5个特征点，每个都有自己的MLP
        batch_size = 5
        gaussians.head_encoder_params = {
            'layers.0.weight': torch.randn(batch_size, 256, 16, device='cuda'),
            'layers.0.bias': torch.randn(batch_size, 256, device='cuda'),
            'layers.2.weight': torch.randn(batch_size, 128, 256, device='cuda'),
            'layers.2.bias': torch.randn(batch_size, 128, device='cuda'),
            'layers.4.weight': torch.randn(batch_size, 128, 128, device='cuda'),
            'layers.4.bias': torch.randn(batch_size, 128, device='cuda'),
            'layers.6.weight': torch.randn(batch_size, 100, 128, device='cuda'),  # 假设num_basis=100
            'layers.6.bias': torch.randn(batch_size, 100, device='cuda')
        }
        gaussians.body_encoder_params = {
            'layers.0.weight': torch.randn(batch_size, 512, 66, device='cuda'),
            'layers.0.bias': torch.randn(batch_size, 512, device='cuda'),
            'layers.2.weight': torch.randn(batch_size, 256, 512, device='cuda'),
            'layers.2.bias': torch.randn(batch_size, 256, device='cuda'),
            'layers.4.weight': torch.randn(batch_size, 256, 256, device='cuda'),
            'layers.4.bias': torch.randn(batch_size, 256, device='cuda'),
            'layers.6.weight': torch.randn(batch_size, 100, 256, device='cuda'),
            'layers.6.bias': torch.randn(batch_size, 100, device='cuda')
        }
        gaussians.head_gs_mask = torch.tensor([True, False, True, False, True], device='cuda')
        gaussians.body_gs_mask = ~gaussians.head_gs_mask
        gaussians._xyz = torch.randn(5, 3, device='cuda')  # 5个Gaussian点
        
        # 添加缺少的属性来支持encoded_feature计算
        gaussians.nbr_gsft = torch.randint(0, 5, (5, 4), device='cuda')  # 5个Gaussian点，每个有4个邻居特征
        gaussians.num_basis = 100
        gaussians.num_vt_basis = 0
        
        print("✓ 模拟GaussianModel设置完成")
        
        # 测试头部身体分离损失
        separation_loss, loss_dict = head_body_separation_loss(gaussians, alpha=1.0, beta=1.0)
        
        print(f"✓ 头部身体分离损失计算成功: {separation_loss.item():.6f}")
        print(f"  - 详细损失项: {list(loss_dict.keys())}")
        for key, value in loss_dict.items():
            print(f"    {key}: {value.item():.6f}")
        
        # 测试头部一致性损失
        head_mask_gt = torch.zeros(128, 128, device='cuda')  # 模拟头部mask
        consistency_loss = head_consistency_loss(gaussians, head_mask_gt, gamma=1.0)
        
        print(f"✓ 头部一致性损失计算成功: {consistency_loss.item():.6f}")
        
        # 测试配置参数 - 创建一个配置对象来检查参数
        try:
            # 创建一个空的配置对象来测试属性
            class MockConfig:
                lambda_head_body_separation = 0.01
                lambda_head_body_feature_diff = 1.0
                lambda_head_body_spatial_sep = 1.0
                lambda_head_consistency = 0.05
                iteration_head_body_loss = 1000
            
            config = MockConfig()
            
            required_attrs = [
                'lambda_head_body_separation',
                'lambda_head_body_feature_diff', 
                'lambda_head_body_spatial_sep',
                'lambda_head_consistency',
                'iteration_head_body_loss'
            ]
            
            for attr in required_attrs:
                if hasattr(config, attr):
                    print(f"✓ Config具有参数: {attr} = {getattr(config, attr)}")
                else:
                    print(f"✗ Config缺少参数: {attr}")
                    return False
        except Exception as e:
            print(f"✗ Config测试失败: {e}")
            return False
        
        print("\n🎉 训练集成测试通过! 头部身体分离功能已正确集成")
        return True
        
    except Exception as e:
        print(f"✗ 训练集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_backward():
    """测试损失函数的反向传播"""
    print("\n开始测试损失函数反向传播...")
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.loss_utils import head_body_separation_loss
        
        # 创建需要梯度的GaussianModel
        gaussians = GaussianModel()
        
        # 设置需要梯度的参数
        gaussians.expression = torch.zeros(10, dtype=torch.float32, requires_grad=True)
        gaussians.jaw_pose = torch.zeros(3, dtype=torch.float32, requires_grad=True)
        gaussians.smpl_poses_cuda = torch.zeros(165, dtype=torch.float32, device='cuda', requires_grad=True)
        
        # 模拟编码器参数 - 使用3D参数
        batch_size = 5
        head_weights = {
            'layers.0.weight': torch.randn(batch_size, 256, 16, device='cuda', requires_grad=True),
            'layers.0.bias': torch.randn(batch_size, 256, device='cuda', requires_grad=True),
            'layers.2.weight': torch.randn(batch_size, 128, 256, device='cuda', requires_grad=True),
            'layers.2.bias': torch.randn(batch_size, 128, device='cuda', requires_grad=True),
            'layers.4.weight': torch.randn(batch_size, 128, 128, device='cuda', requires_grad=True),
            'layers.4.bias': torch.randn(batch_size, 128, device='cuda', requires_grad=True),
            'layers.6.weight': torch.randn(batch_size, 100, 128, device='cuda', requires_grad=True),
            'layers.6.bias': torch.randn(batch_size, 100, device='cuda', requires_grad=True)
        }
        body_weights = {
            'layers.0.weight': torch.randn(batch_size, 512, 66, device='cuda', requires_grad=True),
            'layers.0.bias': torch.randn(batch_size, 512, device='cuda', requires_grad=True),
            'layers.2.weight': torch.randn(batch_size, 256, 512, device='cuda', requires_grad=True),
            'layers.2.bias': torch.randn(batch_size, 256, device='cuda', requires_grad=True),
            'layers.4.weight': torch.randn(batch_size, 256, 256, device='cuda', requires_grad=True),
            'layers.4.bias': torch.randn(batch_size, 256, device='cuda', requires_grad=True),
            'layers.6.weight': torch.randn(batch_size, 100, 256, device='cuda', requires_grad=True),
            'layers.6.bias': torch.randn(batch_size, 100, device='cuda', requires_grad=True)
        }
        gaussians.head_encoder_params = head_weights
        gaussians.body_encoder_params = body_weights
        
        gaussians.head_gs_mask = torch.tensor([True, False, True, False, True], device='cuda')
        gaussians.body_gs_mask = ~gaussians.head_gs_mask
        gaussians._xyz = torch.randn(5, 3, device='cuda', requires_grad=True)
        
        # 添加缺少的属性来支持encoded_feature计算
        gaussians.nbr_gsft = torch.randint(0, 5, (5, 4), device='cuda')  # 5个Gaussian点，每个有4个邻居特征
        gaussians.num_basis = 100
        gaussians.num_vt_basis = 0
        
        # 计算损失
        separation_loss, _ = head_body_separation_loss(gaussians, alpha=1.0, beta=1.0)
        
        # 测试反向传播
        separation_loss.backward()
        
        print("✓ 损失函数反向传播成功")
        print(f"  - expression梯度: {gaussians.expression.grad is not None}")
        print(f"  - jaw_pose梯度: {gaussians.jaw_pose.grad is not None}")
        print(f"  - head_encoder梯度: {head_weights['layers.0.weight'].grad is not None}")
        print(f"  - body_encoder梯度: {body_weights['layers.0.weight'].grad is not None}")
        
        return True
        
    except Exception as e:
        print(f"✗ 反向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("头部身体分离训练集成测试")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_training_integration()
    test2_passed = test_loss_backward()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"训练集成测试: {'✓ 通过' if test1_passed else '✗ 失败'}")
    print(f"反向传播测试: {'✓ 通过' if test2_passed else '✗ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过! 头部身体分离功能训练集成完成")
        exit(0)
    else:
        print("\n❌ 部分测试失败，请检查实现")
        exit(1)