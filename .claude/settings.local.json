{"permissions": {"allow": ["Bash(conda activate:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(source:*)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_head_body_separation.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python /home/<USER>/code/mmlphuman/test_head_body_separation.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python /home/<USER>/code/mmlphuman/test_training_integration.py)", "Bash(find:*)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python /home/<USER>/code/mmlphuman/test_training_ready.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python /home/<USER>/code/mmlphuman/check_dataset.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_actual_training.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_loss_activation.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python final_verification.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_fix.py)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_training_integration.py)", "Bash(/home/<USER>/anaconda3/envs/mmlphuman/bin/python test_sq02_integration.py)"], "deny": []}}